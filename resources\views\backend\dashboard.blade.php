@extends('backend.layouts.app')

@section('title', 'Dashboard')

@push('styles')
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<style>
    /* Ensure stats cards have distinct colors */
    .stats-card-blue { background-color: #3b82f6 !important; }
    .stats-card-green { background-color: #10b981 !important; }
    .stats-card-purple { background-color: #8b5cf6 !important; }
    .stats-card-orange { background-color: #f59e0b !important; }

    /* Force dark text for dashboard content */
    .dashboard-content {
        color: #1f2937 !important;
    }

    .dashboard-content .bg-white,
    .dashboard-content .bg-white * {
        color: #1f2937 !important;
    }

    /* Ensure white text only on colored backgrounds */
    .dashboard-content .stats-card-blue *,
    .dashboard-content .stats-card-green *,
    .dashboard-content .stats-card-purple *,
    .dashboard-content .stats-card-orange * {
        color: white !important;
    }
</style>
@endpush

@section('content')
<div class="dashboard-content">
    <!-- Dashboard Header -->
    <div class="mb-8 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl p-6 text-white shadow-lg">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center">
                    @if($userRole === 'superadmin')
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                    @elseif($userRole === 'employer')
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                    @else
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    @endif
                </div>
                <div>
                    <h1 class="text-4xl font-bold text-white">
                        @if($userRole === 'superadmin')
                            Admin Dashboard
                        @elseif($userRole === 'employer')
                            Employer Dashboard
                        @else
                            Candidate Dashboard
                        @endif
                    </h1>
                    <p class="mt-2 text-lg text-white/90">
                        Welcome back, <span class="font-semibold">{{ auth()->user()->name }}</span>!
                        @if($userRole === 'superadmin')
                            Here's your platform overview.
                        @elseif($userRole === 'employer')
                            Manage your job postings and applications.
                        @else
                            Track your job applications and discover opportunities.
                        @endif
                    </p>
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <div class="text-sm bg-white/20 backdrop-blur-sm px-4 py-3 rounded-xl">
                    <div class="flex items-center space-x-2">
                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        <span class="font-medium text-white">{{ now()->format('M d, Y') }}</span>
                    </div>
                </div>
                <div class="text-sm bg-white/20 backdrop-blur-sm px-4 py-3 rounded-xl">
                    <div class="flex items-center space-x-2">
                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span class="font-medium text-white">{{ now()->format('h:i A') }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        @if($userRole === 'superadmin')
            <!-- Super Admin Stats -->
            <div class="stats-card-blue rounded-lg p-6 text-white shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold">Total Users</h3>
                        <p class="text-3xl font-bold mt-2">{{ number_format($totalUsers ?? 0) }}</p>
                        <div class="mt-3 text-sm text-blue-100">
                            <span>{{ $totalEmployers ?? 0 }} Employers</span> •
                            <span>{{ $totalCandidates ?? 0 }} Candidates</span>
                        </div>
                    </div>
                    <div class="text-blue-200">
                        <svg class="w-10 h-10" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path>
                        </svg>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm text-blue-100">
                    @if($userGrowth['direction'] === 'up')
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L4.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span>+{{ $userGrowth['percentage'] }}% from last month</span>
                    @elseif($userGrowth['direction'] === 'down')
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 10.293a1 1 0 010 1.414l-6 6a1 1 0 01-1.414 0l-6-6a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l4.293-4.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span>-{{ $userGrowth['percentage'] }}% from last month</span>
                    @else
                        <span>No change from last month</span>
                    @endif
                </div>
            </div>

            <div class="stats-card-green rounded-lg p-6 text-white shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold">Job Listings</h3>
                        <p class="text-3xl font-bold mt-2">{{ number_format($totalJobs ?? 0) }}</p>
                        <div class="mt-3 text-sm text-green-100">
                            <span>Active job postings</span>
                        </div>
                    </div>
                    <div class="text-green-200">
                        <svg class="w-10 h-10" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                            <path d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z"></path>
                        </svg>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm text-green-100">
                    @if($jobGrowth['direction'] === 'up')
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L4.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span>+{{ $jobGrowth['percentage'] }}% from last month</span>
                    @elseif($jobGrowth['direction'] === 'down')
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 10.293a1 1 0 010 1.414l-6 6a1 1 0 01-1.414 0l-6-6a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l4.293-4.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span>-{{ $jobGrowth['percentage'] }}% from last month</span>
                    @else
                        <span>No change from last month</span>
                    @endif
                </div>
            </div>

            <div class="stats-card-purple rounded-lg p-6 text-white shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold">Applications</h3>
                        <p class="text-3xl font-bold mt-2">{{ number_format($totalApplications ?? 0) }}</p>
                        <div class="mt-3 text-sm text-purple-100">
                            <span>Total job applications</span>
                        </div>
                    </div>
                    <div class="text-purple-200">
                        <svg class="w-10 h-10" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm text-purple-100">
                    @if($applicationGrowth['direction'] === 'up')
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L4.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span>+{{ $applicationGrowth['percentage'] }}% from last month</span>
                    @elseif($applicationGrowth['direction'] === 'down')
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 10.293a1 1 0 010 1.414l-6 6a1 1 0 01-1.414 0l-6-6a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l4.293-4.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span>-{{ $applicationGrowth['percentage'] }}% from last month</span>
                    @else
                        <span>No change from last month</span>
                    @endif
                </div>
            </div>

            <div class="stats-card-orange rounded-lg p-6 text-white shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold">Companies</h3>
                        <p class="text-3xl font-bold mt-2">{{ number_format($totalCompanies ?? 0) }}</p>
                        <div class="mt-3 text-sm text-orange-100">
                            <span>Registered companies</span>
                        </div>
                    </div>
                    <div class="text-orange-200">
                        <svg class="w-10 h-10" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 4a2 2 0 012-2h8a2 2 0 012 2v8a2 2 0 01-2 2H8a2 2 0 01-2-2V8zm6 4a2 2 0 11-4 0 2 2 0 014 0z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm text-orange-100">
                    @if($companyGrowth['direction'] === 'up')
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L4.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span>+{{ $companyGrowth['percentage'] }}% from last month</span>
                    @elseif($companyGrowth['direction'] === 'down')
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 10.293a1 1 0 010 1.414l-6 6a1 1 0 01-1.414 0l-6-6a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l4.293-4.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span>-{{ $companyGrowth['percentage'] }}% from last month</span>
                    @else
                        <span>No change from last month</span>
                    @endif
                </div>
            </div>
        @elseif($userRole === 'employer')
            <!-- Employer Stats -->
            <div class="stats-card-blue rounded-lg p-6 text-white shadow">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <h3 class="text-lg font-semibold">Company Profile</h3>
                        <p class="text-xl font-bold mt-2 truncate">{{ $company->name ?? 'Not Set' }}</p>
                        <div class="mt-3">
                            <div class="w-full bg-blue-300 rounded-full h-2">
                                <div class="bg-white h-2 rounded-full" style="width: {{ $profileCompletion }}%"></div>
                            </div>
                            <div class="mt-2 text-sm text-blue-100">
                                <span>{{ $profileCompletion }}% Complete</span>
                                @if($company)
                                    <a href="{{ route('companies-listing.edit', $company->id) }}" class="block hover:underline mt-1">Edit Profile</a>
                                @else
                                    <a href="{{ route('companies-listing.create') }}" class="block hover:underline mt-1">Setup your company profile</a>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="text-blue-200">
                        <svg class="w-10 h-10" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 4a2 2 0 012-2h8a2 2 0 012 2v8a2 2 0 01-2 2H8a2 2 0 01-2-2V8zm6 4a2 2 0 11-4 0 2 2 0 014 0z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
            </div>

            <div class="stats-card-green rounded-lg p-6 text-white shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold">Active Jobs</h3>
                        <p class="text-3xl font-bold mt-2">{{ number_format($activeJobs ?? 0) }}</p>
                        <div class="mt-3 text-sm text-green-100">
                            <span>{{ $draftJobs ?? 0 }} Draft</span> •
                            <span>{{ $closedJobs ?? 0 }} Closed</span>
                        </div>
                    </div>
                    <div class="text-green-200">
                        <svg class="w-10 h-10" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                            <path d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z"></path>
                        </svg>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm text-green-100">
                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
                    </svg>
                    <a href="{{ route('jobs-listing.create') }}" class="hover:underline">Post New Job</a>
                </div>
            </div>

            <div class="stats-card-purple rounded-lg p-6 text-white shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold">Applications</h3>
                        <p class="text-3xl font-bold mt-2">{{ number_format($totalApplications ?? 0) }}</p>
                        <div class="mt-3 text-sm text-purple-100">
                            <span>{{ $pendingApplications ?? 0 }} Pending</span> •
                            <span>{{ $shortlistedApplications ?? 0 }} Shortlisted</span>
                        </div>
                    </div>
                    <div class="text-purple-200">
                        <svg class="w-10 h-10" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm text-purple-100">
                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                    <span>{{ $avgApplicationsPerJob }} avg per job</span>
                </div>
            </div>

            <div class="stats-card-orange rounded-lg p-6 text-white shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold">Job Views</h3>
                        <p class="text-3xl font-bold mt-2">{{ number_format($totalViews ?? 0) }}</p>
                        <div class="mt-3 text-sm text-orange-100">
                            <span>{{ $unreadMessages ?? 0 }} Unread Messages</span>
                        </div>
                    </div>
                    <div class="text-orange-200">
                        <svg class="w-10 h-10" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                            <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm text-orange-100">
                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                        <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                    </svg>
                    <a href="{{ route('messages.index') }}" class="hover:underline">View Messages</a>
                </div>
            </div>
        @elseif($userRole === 'candidate')
            <!-- Candidate Stats -->
            <div class="stats-card-blue rounded-lg p-6 text-white shadow">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <h3 class="text-lg font-semibold">Profile Completion</h3>
                        <div class="mt-3 w-full bg-blue-300 rounded-full h-3">
                            <div class="bg-white h-3 rounded-full" style="width: {{ $profileCompletion ?? 0 }}%"></div>
                        </div>
                        <div class="mt-3 text-sm text-blue-100">
                            <span>{{ $profileCompletion ?? 0 }}% Complete</span>
                            @if($profile)
                                <a href="{{ route('candidates.edit', $profile->id) }}" class="block hover:underline mt-1">Complete Your Profile</a>
                            @else
                                <a href="{{ route('candidates.index') }}" class="block hover:underline mt-1">Setup your profile</a>
                            @endif
                        </div>
                    </div>
                    <div class="text-blue-200">
                        <svg class="w-10 h-10" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
            </div>

            <div class="stats-card-green rounded-lg p-6 text-white shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold">Applications</h3>
                        <p class="text-3xl font-bold mt-2">{{ number_format($totalApplications ?? 0) }}</p>
                        <div class="mt-3 text-sm text-green-100">
                            <span>{{ $pendingApplications ?? 0 }} Pending</span> •
                            <span>{{ $shortlistedApplications ?? 0 }} Shortlisted</span>
                        </div>
                    </div>
                    <div class="text-green-200">
                        <svg class="w-10 h-10" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm text-green-100">
                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                    </svg>
                    <span>{{ $successRate }}% success rate</span>
                </div>
            </div>

            <div class="stats-card-purple rounded-lg p-6 text-white shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold">Application Status</h3>
                        <p class="text-3xl font-bold mt-2">{{ number_format($hiredApplications ?? 0) }}</p>
                        <div class="mt-3 text-sm text-purple-100">
                            <span>{{ $rejectedApplications ?? 0 }} Rejected</span> •
                            <span>Hired</span>
                        </div>
                    </div>
                    <div class="text-purple-200">
                        <svg class="w-10 h-10" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm text-purple-100">
                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    <span>{{ ($hiredApplications ?? 0) > 0 ? 'Congratulations!' : 'Keep trying!' }}</span>
                </div>
            </div>

            <div class="stats-card-orange rounded-lg p-6 text-white shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold">Messages</h3>
                        <p class="text-3xl font-bold mt-2">{{ number_format($unreadMessages ?? 0) }}</p>
                        <div class="mt-3 text-sm text-orange-100">
                            <span>{{ $savedJobs ?? 0 }} Saved Jobs</span>
                        </div>
                    </div>
                    <div class="text-orange-200">
                        <svg class="w-10 h-10" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                        </svg>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm text-orange-100">
                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z"></path>
                    </svg>
                    <a href="{{ route('messages.index') }}" class="hover:underline">View Messages</a>
                </div>
            </div>
        @endif
    </div>

    @if($userRole === 'superadmin')
        <!-- Analytics Chart Section -->
        <div class="mb-8">
            <div class="bg-white rounded-lg shadow p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-6">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-indigo-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-gray-900">Platform Analytics</h3>
                            <p class="text-gray-600">Track your platform's growth and performance</p>
                        </div>
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-4 py-2 text-sm bg-indigo-500 text-white rounded-lg">6 Months</button>
                        <button class="px-4 py-2 text-sm text-gray-600 hover:bg-gray-100 rounded-lg">1 Year</button>
                    </div>
                </div>
                <div class="h-96 relative">
                    <canvas id="analyticsChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Top Performers Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- Top Companies -->
            <div class="bg-white rounded-lg shadow p-6 border border-gray-200">
                <div class="flex items-center space-x-3 mb-6">
                    <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 4a2 2 0 012-2h8a2 2 0 012 2v8a2 2 0 01-2 2H8a2 2 0 01-2-2V8zm6 4a2 2 0 11-4 0 2 2 0 014 0z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold text-gray-900">Top Companies</h3>
                        <p class="text-gray-600 text-sm">Most active employers</p>
                    </div>
                </div>
                <div class="space-y-3">
                    @forelse($topCompanies ?? [] as $company)
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 4a2 2 0 012-2h8a2 2 0 012 2v8a2 2 0 01-2 2H8a2 2 0 01-2-2V8zm6 4a2 2 0 11-4 0 2 2 0 014 0z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">{{ $company->name }}</p>
                                    <p class="text-sm text-gray-500">{{ $company->job_listings_count }} jobs posted</p>
                                </div>
                            </div>
                            <span class="text-sm font-medium text-blue-600">{{ $company->job_listings_count }}</span>
                        </div>
                    @empty
                        <p class="text-gray-500 text-center py-4">No companies yet</p>
                    @endforelse
                </div>
            </div>

            <!-- Top Jobs -->
            <div class="bg-white rounded-lg shadow p-6 border border-gray-200">
                <div class="flex items-center space-x-3 mb-6">
                    <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                            <path d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold text-gray-900">Most Applied Jobs</h3>
                        <p class="text-gray-600 text-sm">Popular job listings</p>
                    </div>
                </div>
                <div class="space-y-3">
                    @forelse($topJobs ?? [] as $job)
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                                        <path d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">{{ Str::limit($job->title, 30) }}</p>
                                    <p class="text-sm text-gray-500">{{ $job->company->name ?? 'No Company' }}</p>
                                </div>
                            </div>
                            <span class="text-sm font-medium text-green-600">{{ $job->applications_count }} apps</span>
                        </div>
                    @empty
                        <p class="text-gray-500 text-center py-4">No jobs yet</p>
                    @endforelse
                </div>
            </div>
        </div>
    @endif

    <!-- Main Content -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        @if($userRole === 'superadmin')
            <!-- Recent Job Listings -->
            <div class="bg-white rounded-lg shadow p-6 border border-gray-200">
                <div class="flex justify-between items-center mb-6">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                                <path d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-bold text-gray-900">Recent Job Listings</h3>
                    </div>
                    <a href="{{ route('jobs-listing.index') }}" class="text-sm text-indigo-600 hover:text-indigo-800 font-medium">View All →</a>
                </div>

                @if(isset($recentJobs) && $recentJobs->count() > 0)
                    <div class="space-y-4">
                        @foreach($recentJobs as $job)
                            <div class="border-b pb-3">
                                <div class="flex justify-between">
                                    <h4 class="font-medium">{{ $job->title }}</h4>
                                    <span class="text-sm text-gray-500">{{ $job->created_at->diffForHumans() }}</span>
                                </div>
                                <p class="text-sm text-gray-600">{{ $job->company->name ?? 'No Company' }}</p>
                                <div class="mt-2 flex justify-between">
                                    <span class="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full">
                                        {{ $job->job_type }}
                                    </span>
                                    <a href="{{ route('jobs-listing.show', $job) }}" class="text-xs text-indigo-600 hover:text-indigo-800">View Details</a>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-gray-500">No job listings yet.</p>
                @endif
            </div>

            <!-- Recent Applications -->
            <div class="bg-white rounded-lg shadow p-6 border border-gray-200">
                <div class="flex justify-between items-center mb-6">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
                            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-bold text-gray-900">Recent Applications</h3>
                    </div>
                    <a href="{{ route('applications.index') }}" class="text-sm text-indigo-600 hover:text-indigo-800 font-medium">View All →</a>
                </div>

                @if(isset($recentApplications) && $recentApplications->count() > 0)
                    <div class="space-y-4">
                        @foreach($recentApplications as $application)
                            <div class="border-b pb-3">
                                <div class="flex justify-between">
                                    <h4 class="font-medium">{{ $application->user->name }}</h4>
                                    <span class="text-sm text-gray-500">{{ $application->created_at->diffForHumans() }}</span>
                                </div>
                                <p class="text-sm text-gray-600">Applied for: {{ $application->jobListing->title }}</p>
                                <div class="mt-2 flex justify-between">
                                    <span class="text-xs px-2 py-1 bg-green-100 text-green-800 rounded-full">
                                        {{ $application->status }}
                                    </span>
                                    <a href="{{ route('applications.show', $application) }}" class="text-xs text-indigo-600 hover:text-indigo-800">View Application</a>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-gray-500">No applications yet.</p>
                @endif
            </div>
        @elseif($userRole === 'employer')
            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow p-6 border border-gray-200">
                <div class="flex items-center space-x-3 mb-6">
                    <div class="w-8 h-8 bg-indigo-500 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-bold text-gray-900">Quick Actions</h3>
                </div>
                <div class="grid grid-cols-2 gap-4">
                    <a href="{{ route('jobs-listing.create') }}" class="block p-4 bg-indigo-50 rounded-lg hover:bg-indigo-100">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
                                <svg class="w-4 h-4 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <div class="text-indigo-600 font-medium">Post a New Job</div>
                                <p class="text-sm text-gray-600 mt-1">Create a new job listing</p>
                            </div>
                        </div>
                    </a>

                    @if($company)
                        <a href="{{ route('companies-listing.edit', $company->id) }}" class="block p-4 bg-indigo-50 rounded-lg hover:bg-indigo-100">
                    @else
                        <a href="{{ route('companies-listing.create') }}" class="block p-4 bg-indigo-50 rounded-lg hover:bg-indigo-100">
                    @endif
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
                                <svg class="w-4 h-4 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 4a2 2 0 012-2h8a2 2 0 012 2v8a2 2 0 01-2 2H8a2 2 0 01-2-2V8zm6 4a2 2 0 11-4 0 2 2 0 014 0z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <div class="text-indigo-600 font-medium">{{ $company ? 'Edit' : 'Setup' }} Company Profile</div>
                                <p class="text-sm text-gray-600 mt-1">{{ $company ? 'Update' : 'Create' }} your company information</p>
                            </div>
                        </div>
                    </a>

                    <a href="{{ route('candidates.index') }}" class="block p-4 bg-indigo-50 rounded-lg hover:bg-indigo-100">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
                                <svg class="w-4 h-4 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path>
                                </svg>
                            </div>
                            <div>
                                <div class="text-indigo-600 font-medium">Browse Candidates</div>
                                <p class="text-sm text-gray-600 mt-1">Find potential employees</p>
                            </div>
                        </div>
                    </a>

                    <a href="{{ route('jobs-listing.index') }}" class="block p-4 bg-indigo-50 rounded-lg hover:bg-indigo-100">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
                                <svg class="w-4 h-4 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                                    <path d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z"></path>
                                </svg>
                            </div>
                            <div>
                                <div class="text-indigo-600 font-medium">Manage Job Listings</div>
                                <p class="text-sm text-gray-600 mt-1">Edit or remove your listings</p>
                            </div>
                        </div>
                    </a>
                </div>
            </div>

            <!-- Recent Applications -->
            <div class="bg-white rounded-lg shadow p-6 border border-gray-200">
                <div class="flex justify-between items-center mb-6">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-bold text-gray-900">Recent Applications</h3>
                    </div>
                    <a href="{{ route('applications.index') }}" class="text-sm text-indigo-600 hover:text-indigo-800 font-medium">View All →</a>
                </div>

                @if(isset($recentApplications) && $recentApplications->count() > 0)
                    <div class="space-y-4">
                        @foreach($recentApplications as $application)
                            <div class="border-b pb-3">
                                <div class="flex justify-between">
                                    <h4 class="font-medium">{{ $application->user->name }}</h4>
                                    <span class="text-sm text-gray-500">{{ $application->created_at->diffForHumans() }}</span>
                                </div>
                                <p class="text-sm text-gray-600">Applied for: {{ $application->jobListing->title }}</p>
                                <div class="mt-2 flex justify-between">
                                    <span class="text-xs px-2 py-1 bg-green-100 text-green-800 rounded-full">
                                        {{ $application->status }}
                                    </span>
                                    <a href="{{ route('applications.show', $application) }}" class="text-xs text-indigo-600 hover:text-indigo-800">View Application</a>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-gray-500">No applications yet.</p>
                @endif
            </div>
        @elseif($userRole === 'candidate')
            <!-- Recommended Jobs -->
            <div class="bg-white rounded-lg shadow p-6 border border-gray-200">
                <div class="flex justify-between items-center mb-6">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-bold text-gray-900">Recommended Jobs</h3>
                    </div>
                    <a href="{{ route('jobs-listing.index') }}" class="text-sm text-indigo-600 hover:text-indigo-800 font-medium">Browse All →</a>
                </div>

                @if(isset($recommendedJobs) && $recommendedJobs->count() > 0)
                    <div class="space-y-4">
                        @foreach($recommendedJobs as $job)
                            <div class="border-b pb-3">
                                <div class="flex justify-between">
                                    <h4 class="font-medium">{{ $job->title }}</h4>
                                    <span class="text-sm text-gray-500">{{ $job->created_at->diffForHumans() }}</span>
                                </div>
                                <p class="text-sm text-gray-600">{{ $job->company->name ?? 'No Company' }} • {{ $job->location }}</p>
                                <div class="mt-2 flex justify-between">
                                    <span class="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full">
                                        {{ $job->job_type }}
                                    </span>
                                    <div>
                                        <a href="#" class="text-xs text-gray-600 hover:text-gray-800 mr-2">Save</a>
                                        <a href="{{ route('frontend.job.show', $job->slug) }}" class="text-xs text-indigo-600 hover:text-indigo-800">Apply Now</a>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-gray-500">No recommended jobs available.</p>
                @endif
            </div>

            <!-- Your Applications -->
            <div class="bg-white rounded-lg shadow p-6 border border-gray-200">
                <div class="flex justify-between items-center mb-6">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
                            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-bold text-gray-900">Your Applications</h3>
                    </div>
                    <a href="{{ route('applications.index') }}" class="text-sm text-indigo-600 hover:text-indigo-800 font-medium">View All →</a>
                </div>

                @if(isset($recentApplications) && $recentApplications->count() > 0)
                    <div class="space-y-4">
                        @foreach($recentApplications as $application)
                            <div class="border-b pb-3">
                                <div class="flex justify-between">
                                    <h4 class="font-medium">{{ $application->jobListing->title }}</h4>
                                    <span class="text-sm text-gray-500">{{ $application->created_at->diffForHumans() }}</span>
                                </div>
                                <p class="text-sm text-gray-600">{{ $application->jobListing->company->name ?? 'No Company' }}</p>
                                <div class="mt-2 flex justify-between">
                                    <span class="text-xs px-2 py-1
                                        @if($application->status == 'pending') bg-yellow-100 text-yellow-800
                                        @elseif($application->status == 'reviewed') bg-blue-100 text-blue-800
                                        @elseif($application->status == 'rejected') bg-red-100 text-red-800
                                        @elseif($application->status == 'shortlisted') bg-green-100 text-green-800
                                        @else bg-gray-100 text-gray-800 @endif
                                        rounded-full">
                                        {{ ucfirst($application->status) }}
                                    </span>
                                    <a href="{{ route('applications.show', $application) }}" class="text-xs text-indigo-600 hover:text-indigo-800">View Details</a>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-gray-500">You haven't applied to any jobs yet.</p>
                @endif
            </div>
        @endif
    </div>
</div>
@endsection

@push('scripts')
@if($userRole === 'superadmin' && isset($chartData))
<script>
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('analyticsChart').getContext('2d');

    const chartData = @json($chartData);

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: chartData.months,
            datasets: [
                {
                    label: 'New Users',
                    data: chartData.users,
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                },
                {
                    label: 'Job Listings',
                    data: chartData.jobs,
                    borderColor: '#10b981',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                },
                {
                    label: 'Applications',
                    data: chartData.applications,
                    borderColor: '#8b5cf6',
                    backgroundColor: 'rgba(139, 92, 246, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 20
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            },
            elements: {
                point: {
                    radius: 6,
                    hoverRadius: 8
                }
            }
        }
    });
});
</script>
@endif
@endpush
