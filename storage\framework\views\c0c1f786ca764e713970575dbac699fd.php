

<?php $__env->startSection('title', 'Jobs-Recruitment-Employment-Career-Courses-Professionals | JobOctopus'); ?>
<?php $__env->startSection('meta_description', 'Find your dream job with JobOctopus - Jobs, Recruitment, Employment, Career opportunities, Courses and Professional development'); ?>
<?php $__env->startSection('meta_keywords', 'jobs, recruitment, employment, career, courses, professionals, job search'); ?>

<?php $__env->startSection('content'); ?>

    <!-- Hero/Banner Section with Enhanced Gradient -->
    <section class="gradient-bg relative overflow-hidden" style="padding: 80px 0 120px 0;">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25% 25%, white 2px, transparent 2px); background-size: 50px 50px;"></div>
        </div>

        <!-- Floating Elements -->
        <div class="absolute top-20 left-10 w-20 h-20 bg-white opacity-10 rounded-full" style="filter: blur(20px);"></div>
        <div class="absolute bottom-20 right-10 w-32 h-32 bg-white opacity-10 rounded-full" style="filter: blur(30px);"></div>
        <div class="absolute top-1/2 right-1/4 w-16 h-16 bg-white opacity-10 rounded-full" style="filter: blur(15px);"></div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center" style="margin-bottom: 60px;">
                <h1 class="text-white font-bold leading-tight" style="font-size: clamp(2.5rem, 5vw, 4rem); margin-bottom: 24px;">
                    Find Your <span class="text-yellow-300">Dream Job</span>
                </h1>
                <p class="text-white max-w-3xl mx-auto leading-relaxed" style="font-size: clamp(1.125rem, 2vw, 1.5rem); opacity: 0.9; margin-bottom: 40px;">
                    Discover thousands of opportunities from top companies and kickstart your career journey today
                </p>
            </div>

            <div class="flex items-center justify-center">
                <div class="w-full max-w-5xl">
                    <!-- Search Form -->
                    <form action="<?php echo e(url('search_page')); ?>" method="GET" style="margin-bottom: 40px;">
                        <div class="bg-white rounded-2xl shadow-2xl" style="padding: 32px; background: rgba(255, 255, 255, 0.95);">
                            <div class="flex flex-col md:flex-row gap-4">
                                <!-- Keywords Input -->
                                <div class="flex-1">
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                            <i class="fa fa-search text-gray-400 text-lg"></i>
                                        </div>
                                        <input
                                            type="text"
                                            name="keywords"
                                            placeholder="Enter Keywords, Skills, Designation, Company..."
                                            class="w-full rounded-xl border-0 text-gray-700 shadow-lg text-lg placeholder-gray-500 bg-white"
                                            style="padding: 20px 24px 20px 48px; outline: none; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);"
                                            onfocus="this.style.boxShadow='0 0 0 4px rgba(255, 255, 255, 0.3), 0 4px 6px -1px rgba(0, 0, 0, 0.1)'"
                                            onblur="this.style.boxShadow='0 4px 6px -1px rgba(0, 0, 0, 0.1)'"
                                        >
                                    </div>
                                </div>

                                <!-- Search Button -->
                                <div class="flex-shrink-0">
                                    <button
                                        type="submit"
                                        class="w-full md:w-auto red-gradient text-white rounded-xl font-bold flex items-center justify-center shadow-xl text-lg transition-all duration-300"
                                        style="padding: 20px 32px;"
                                        onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 25px 50px -12px rgba(0, 0, 0, 0.25)'"
                                        onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 20px 25px -5px rgba(0, 0, 0, 0.1)'"
                                    >
                                        <i class="fa fa-search mr-3"></i>
                                        Search Jobs
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Popular Searches -->
                        <div class="text-center" style="margin-top: 40px;">
                            <p class="text-white font-medium" style="opacity: 0.8; margin-bottom: 20px; font-size: 14px;">Popular Searches:</p>
                            <div class="flex flex-wrap justify-center gap-3">
                                <a href="<?php echo e(url('volunteering')); ?>" class="flex items-center space-x-2 text-white text-sm transition-all duration-300 rounded-full border border-white" style="background: rgba(255, 255, 255, 0.15); padding: 10px 16px; border-color: rgba(255, 255, 255, 0.2);" onmouseover="this.style.background='rgba(255, 255, 255, 0.25)'; this.style.transform='scale(1.05)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.15)'; this.style.transform='scale(1)'">
                                    <i class="fa fa-heart"></i>
                                    <span>Volunteering</span>
                                </a>
                                <a href="<?php echo e(url('casual')); ?>" class="flex items-center space-x-2 text-white text-sm transition-all duration-300 rounded-full border border-white" style="background: rgba(255, 255, 255, 0.15); padding: 10px 16px; border-color: rgba(255, 255, 255, 0.2);" onmouseover="this.style.background='rgba(255, 255, 255, 0.25)'; this.style.transform='scale(1.05)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.15)'; this.style.transform='scale(1)'">
                                    <i class="fa fa-clock-o"></i>
                                    <span>Casual</span>
                                </a>
                                <a href="<?php echo e(url('internship')); ?>" class="flex items-center space-x-2 text-white text-sm transition-all duration-300 rounded-full border border-white" style="background: rgba(255, 255, 255, 0.15); padding: 10px 16px; border-color: rgba(255, 255, 255, 0.2);" onmouseover="this.style.background='rgba(255, 255, 255, 0.25)'; this.style.transform='scale(1.05)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.15)'; this.style.transform='scale(1)'">
                                    <i class="fa fa-graduation-cap"></i>
                                    <span>Internship</span>
                                </a>
                                <a href="<?php echo e(url('business')); ?>" class="flex items-center space-x-2 text-white text-sm transition-all duration-300 rounded-full border border-white" style="background: rgba(255, 255, 255, 0.15); padding: 10px 16px; border-color: rgba(255, 255, 255, 0.2);" onmouseover="this.style.background='rgba(255, 255, 255, 0.25)'; this.style.transform='scale(1.05)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.15)'; this.style.transform='scale(1)'">
                                    <i class="fa fa-building"></i>
                                    <span>Business</span>
                                </a>
                                <a href="<?php echo e(url('franchise')); ?>" class="flex items-center space-x-2 text-white text-sm transition-all duration-300 rounded-full border border-white" style="background: rgba(255, 255, 255, 0.15); padding: 10px 16px; border-color: rgba(255, 255, 255, 0.2);" onmouseover="this.style.background='rgba(255, 255, 255, 0.25)'; this.style.transform='scale(1.05)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.15)'; this.style.transform='scale(1)'">
                                    <i class="fa fa-handshake-o"></i>
                                    <span>Franchise</span>
                                </a>
                                <a href="<?php echo e(url('part_time')); ?>" class="flex items-center space-x-2 text-white text-sm transition-all duration-300 rounded-full border border-white" style="background: rgba(255, 255, 255, 0.15); padding: 10px 16px; border-color: rgba(255, 255, 255, 0.2);" onmouseover="this.style.background='rgba(255, 255, 255, 0.25)'; this.style.transform='scale(1.05)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.15)'; this.style.transform='scale(1)'">
                                    <i class="fa fa-calendar"></i>
                                    <span>Part-time</span>
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <!-- Featured Job Categories - Full Width -->
        <?php if(isset($featuredCategories) && $featuredCategories->count() > 0): ?>
            <section class="mb-12">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-4">Featured Job Categories</h2>
                    <p class="text-gray-600 max-w-2xl mx-auto">Explore opportunities across various industries and find your perfect career match</p>
                </div>
                <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                    <?php $__currentLoopData = $featuredCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <a href="<?php echo e(url('category/' . $category->slug)); ?>" class="category-icon category-gradient rounded-xl p-6 text-center shadow-sm hover:shadow-xl transition-all border border-blue-100 group">
                            <div class="w-16 h-16 mx-auto mb-4 icon-gradient rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                                <i class="fa fa-briefcase text-white text-2xl"></i>
                            </div>
                            <h3 class="font-semibold text-gray-900 text-sm mb-1"><?php echo e($category->name); ?></h3>
                            <p class="text-xs text-blue-600 font-medium"><?php echo e($category->jobs_count ?? 0); ?> Jobs</p>
                        </a>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </section>
        <?php else: ?>
            <section class="mb-12">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-4">Featured Job Categories</h2>
                    <p class="text-gray-600 max-w-2xl mx-auto">Explore opportunities across various industries and find your perfect career match</p>
                </div>
                <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                    <a href="<?php echo e(url('accounting')); ?>" class="category-icon category-gradient rounded-xl p-6 text-center shadow-sm hover:shadow-xl transition-all border border-blue-100 group">
                        <div class="w-16 h-16 mx-auto mb-4 icon-gradient rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                            <i class="fa fa-calculator text-white text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-gray-900 text-sm mb-1">Accounting</h3>
                        <p class="text-xs text-blue-600 font-medium">25 Jobs</p>
                    </a>
                    <a href="<?php echo e(url('development')); ?>" class="category-icon category-gradient rounded-xl p-6 text-center shadow-sm hover:shadow-xl transition-all border border-blue-100 group">
                        <div class="w-16 h-16 mx-auto mb-4 icon-gradient rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                            <i class="fa fa-code text-white text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-gray-900 text-sm mb-1">Development</h3>
                        <p class="text-xs text-blue-600 font-medium">45 Jobs</p>
                    </a>
                    <a href="<?php echo e(url('technology')); ?>" class="category-icon category-gradient rounded-xl p-6 text-center shadow-sm hover:shadow-xl transition-all border border-blue-100 group">
                        <div class="w-16 h-16 mx-auto mb-4 icon-gradient rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                            <i class="fa fa-laptop text-white text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-gray-900 text-sm mb-1">Technology</h3>
                        <p class="text-xs text-blue-600 font-medium">38 Jobs</p>
                    </a>
                    <a href="<?php echo e(url('media_news')); ?>" class="category-icon category-gradient rounded-xl p-6 text-center shadow-sm hover:shadow-xl transition-all border border-blue-100 group">
                        <div class="w-16 h-16 mx-auto mb-4 icon-gradient rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                            <i class="fa fa-newspaper-o text-white text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-gray-900 text-sm mb-1">Media & News</h3>
                        <p class="text-xs text-blue-600 font-medium">12 Jobs</p>
                    </a>
                    <a href="<?php echo e(url('medical')); ?>" class="category-icon category-gradient rounded-xl p-6 text-center shadow-sm hover:shadow-xl transition-all border border-blue-100 group">
                        <div class="w-16 h-16 mx-auto mb-4 icon-gradient rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                            <i class="fa fa-stethoscope text-white text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-gray-900 text-sm mb-1">Medical</h3>
                        <p class="text-xs text-blue-600 font-medium">22 Jobs</p>
                    </a>
                    <a href="<?php echo e(url('govt')); ?>" class="category-icon category-gradient rounded-xl p-6 text-center shadow-sm hover:shadow-xl transition-all border border-blue-100 group">
                        <div class="w-16 h-16 mx-auto mb-4 icon-gradient rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                            <i class="fa fa-university text-white text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-gray-900 text-sm mb-1">Government</h3>
                        <p class="text-xs text-blue-600 font-medium">18 Jobs</p>
                    </a>
                </div>
            </section>
        <?php endif; ?>



        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- Main Content Area -->
            <div class="lg:col-span-3">

                <!-- Recent Jobs -->
                <section>
                    <div class="flex justify-between items-center mb-6">
                        <div>
                            <h2 class="text-2xl font-bold text-gray-900 mb-2">Recent Jobs</h2>
                            <p class="text-gray-600">Latest opportunities from top companies</p>
                        </div>
                        <a href="<?php echo e(url('jobs')); ?>" class="text-blue-600 hover:text-blue-700 font-medium flex items-center transition-colors">
                            View All Jobs
                            <i class="fa fa-arrow-right ml-2"></i>
                        </a>
                    </div>

                    <div class="space-y-4">
                        <?php if(isset($recentJobs) && $recentJobs->count() > 0): ?>
                            <?php $__currentLoopData = $recentJobs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $job): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="job-card bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-lg transition-all duration-300 relative overflow-hidden">
                                    <!-- Decorative gradient line -->
                                    <div class="absolute top-0 left-0 w-full h-1 gradient-bg"></div>

                                    <div class="p-6">
                                        <div class="flex items-start gap-4 mb-4">
                                            <!-- Company Logo -->
                                            <div class="flex-shrink-0">
                                                <?php if($job->company && $job->company->logo): ?>
                                                    <img src="<?php echo e(url('storage/' . $job->company->logo)); ?>" alt="<?php echo e($job->company->name); ?>" class="w-14 h-14 object-contain rounded-lg border border-gray-200 bg-white">
                                                <?php else: ?>
                                                    <div class="w-14 h-14 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                                                        <i class="fa fa-building text-white text-lg"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </div>

                                            <!-- Job Details -->
                                            <div class="flex-1 min-w-0">
                                                <div class="flex items-start justify-between mb-2">
                                                    <div class="flex-1 min-w-0">
                                                        <h3 class="text-xl font-semibold text-gray-900 mb-1 hover:text-blue-600 transition-colors">
                                                            <a href="<?php echo e(route('frontend.job.show', $job->slug)); ?>" class="block truncate"><?php echo e($job->title); ?></a>
                                                        </h3>
                                                        <p class="text-gray-600 font-medium text-base"><?php echo e($job->company->name ?? 'Company Name'); ?></p>
                                                    </div>
                                                    <!-- Posted Time -->
                                                    <div class="flex-shrink-0 ml-4">
                                                        <span class="text-xs text-gray-400 bg-gray-50 px-2 py-1 rounded-md"><?php echo e($job->created_at->diffForHumans()); ?></span>
                                                    </div>
                                                </div>

                                                <!-- Job Meta Info -->
                                                <div class="flex flex-wrap gap-2 text-sm text-gray-500 mb-3">
                                                    <?php if($job->experience_level): ?>
                                                        <span class="flex items-center job-meta-tag px-2.5 py-1.5 rounded-md">
                                                            <i class="fa fa-briefcase mr-1.5 text-gray-400"></i>
                                                            <?php echo e($job->experience_level); ?>

                                                        </span>
                                                    <?php endif; ?>
                                                    <?php if($job->location): ?>
                                                        <span class="flex items-center job-meta-tag px-2.5 py-1.5 rounded-md">
                                                            <i class="fa fa-map-marker mr-1.5 text-gray-400"></i>
                                                            <?php echo e($job->location); ?>

                                                        </span>
                                                    <?php endif; ?>
                                                    <?php if($job->job_type): ?>
                                                        <span class="flex items-center job-meta-tag px-2.5 py-1.5 rounded-md">
                                                            <i class="fa fa-clock-o mr-1.5 text-gray-400"></i>
                                                            <?php echo e(ucfirst($job->job_type)); ?>

                                                        </span>
                                                    <?php endif; ?>
                                                </div>

                                                <?php if($job->salary_min && $job->salary_max): ?>
                                                    <div class="mb-3">
                                                        <span class="inline-flex items-center px-3 py-1.5 rounded-lg text-sm font-semibold bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-sm">
                                                            <i class="fa fa-money mr-2"></i>
                                                            ₹<?php echo e(number_format($job->salary_min)); ?> - ₹<?php echo e(number_format($job->salary_max)); ?>

                                                        </span>
                                                    </div>
                                                <?php endif; ?>

                                                <?php if($job->skills_required && is_array($job->skills_required)): ?>
                                                    <div class="mb-4">
                                                        <div class="flex flex-wrap gap-2">
                                                            <?php $__currentLoopData = array_slice($job->skills_required, 0, 4); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $skill): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                <span class="skill-tag px-2.5 py-1 bg-blue-50 text-blue-700 text-xs rounded-md border border-blue-200 font-medium"><?php echo e($skill); ?></span>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            <?php if(count($job->skills_required) > 4): ?>
                                                                <span class="px-2.5 py-1 bg-gray-100 text-gray-600 text-xs rounded-md font-medium">+<?php echo e(count($job->skills_required) - 4); ?> more</span>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                <?php endif; ?>

                                                <?php if($job->description): ?>
                                                    <p class="text-gray-600 text-sm leading-relaxed line-clamp-2">
                                                        <?php echo e(Str::limit(strip_tags($job->description), 120)); ?>

                                                    </p>
                                                <?php endif; ?>
                                            </div>
                                        </div>

                                        <!-- Action Buttons -->
                                        <div class="flex items-center justify-between pt-4 border-t border-gray-100">
                                            <div class="flex items-center gap-3">
                                                <a href="<?php echo e(route('frontend.job.show', $job->slug)); ?>" class="red-gradient text-white px-6 py-2.5 rounded-lg hover:shadow-lg transition-all duration-300 text-sm font-semibold inline-block">
                                                    <i class="fa fa-paper-plane mr-2"></i>
                                                    Apply Now
                                                </a>
                                                <button class="border border-gray-300 text-gray-700 px-4 py-2.5 rounded-lg hover:bg-gray-50 hover:border-gray-400 transition-all duration-300 text-sm font-medium">
                                                    <i class="fa fa-bookmark-o mr-1.5"></i>
                                                    Save
                                                </button>
                                            </div>
                                            <div class="flex items-center gap-4 text-sm text-gray-400">
                                                <button class="hover:text-red-500 transition-colors p-1">
                                                    <i class="fa fa-heart-o text-base"></i>
                                                </button>
                                                <span class="flex items-center bg-gray-50 px-2 py-1 rounded-md">
                                                    <i class="fa fa-eye mr-1.5"></i>
                                                    <?php echo e($job->views_count ?? 0); ?>

                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                            <!-- Sample Job Cards for Demo -->
                            <div class="job-card bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-lg transition-all duration-300 relative overflow-hidden">
                                <!-- Decorative gradient line -->
                                <div class="absolute top-0 left-0 w-full h-1 gradient-bg"></div>

                                <div class="p-6">
                                    <div class="flex items-start gap-4 mb-4">
                                        <!-- Company Logo -->
                                        <div class="flex-shrink-0">
                                            <div class="w-14 h-14 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                                                <i class="fa fa-building text-white text-lg"></i>
                                            </div>
                                        </div>

                                        <!-- Job Details -->
                                        <div class="flex-1 min-w-0">
                                            <div class="flex items-start justify-between mb-2">
                                                <div class="flex-1 min-w-0">
                                                    <h3 class="text-xl font-semibold text-gray-900 mb-1 hover:text-blue-600 transition-colors">
                                                        <a href="#" class="block truncate">Java Developer</a>
                                                    </h3>
                                                    <p class="text-gray-600 font-medium text-base">ZHANOX INFOTECH PVT LTD.</p>
                                                </div>
                                                <!-- Posted Time -->
                                                <div class="flex-shrink-0 ml-4">
                                                    <span class="text-xs text-gray-400 bg-gray-50 px-2 py-1 rounded-md">2 days ago</span>
                                                </div>
                                            </div>

                                            <!-- Job Meta Info -->
                                            <div class="flex flex-wrap gap-2 text-sm text-gray-500 mb-3">
                                                <span class="flex items-center job-meta-tag px-2.5 py-1.5 rounded-md">
                                                    <i class="fa fa-briefcase mr-1.5 text-gray-400"></i>
                                                    0-4 years
                                                </span>
                                                <span class="flex items-center job-meta-tag px-2.5 py-1.5 rounded-md">
                                                    <i class="fa fa-map-marker mr-1.5 text-gray-400"></i>
                                                    Bengaluru / Bangalore
                                                </span>
                                                <span class="flex items-center job-meta-tag px-2.5 py-1.5 rounded-md">
                                                    <i class="fa fa-clock-o mr-1.5 text-gray-400"></i>
                                                    Full-time
                                                </span>
                                            </div>

                                            <div class="mb-4">
                                                <div class="flex flex-wrap gap-2">
                                                    <span class="skill-tag px-2.5 py-1 bg-blue-50 text-blue-700 text-xs rounded-md border border-blue-200 font-medium">Java</span>
                                                    <span class="skill-tag px-2.5 py-1 bg-blue-50 text-blue-700 text-xs rounded-md border border-blue-200 font-medium">JavaScript</span>
                                                    <span class="skill-tag px-2.5 py-1 bg-blue-50 text-blue-700 text-xs rounded-md border border-blue-200 font-medium">Angular</span>
                                                    <span class="skill-tag px-2.5 py-1 bg-blue-50 text-blue-700 text-xs rounded-md border border-blue-200 font-medium">Spring Boot</span>
                                                </div>
                                            </div>

                                            <p class="text-gray-600 text-sm leading-relaxed line-clamp-2">
                                                We are looking for a talented Java developer with good understanding of Object-Oriented Programming...
                                            </p>
                                        </div>
                                    </div>

                                    <!-- Action Buttons -->
                                    <div class="flex items-center justify-between pt-4 border-t border-gray-100">
                                        <div class="flex items-center gap-3">
                                            <button class="red-gradient text-white px-6 py-2.5 rounded-lg hover:shadow-lg transition-all duration-300 text-sm font-semibold">
                                                <i class="fa fa-paper-plane mr-2"></i>
                                                Apply Now
                                            </button>
                                            <button class="border border-gray-300 text-gray-700 px-4 py-2.5 rounded-lg hover:bg-gray-50 hover:border-gray-400 transition-all duration-300 text-sm font-medium">
                                                <i class="fa fa-bookmark-o mr-1.5"></i>
                                                Save
                                            </button>
                                        </div>
                                        <div class="flex items-center gap-4 text-sm text-gray-400">
                                            <button class="hover:text-red-500 transition-colors p-1">
                                                <i class="fa fa-heart-o text-base"></i>
                                            </button>
                                            <span class="flex items-center bg-gray-50 px-2 py-1 rounded-md">
                                                <i class="fa fa-eye mr-1.5"></i>
                                                15
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </section>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">

                <!-- Top Hiring Companies -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6 hover:shadow-md transition-shadow">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-gray-900">Top Hiring Companies</h3>
                        <div class="w-8 h-8 icon-gradient rounded-lg flex items-center justify-center">
                            <i class="fa fa-building text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <?php if(isset($topRecruiters) && $topRecruiters->count() > 0): ?>
                            <?php $__currentLoopData = $topRecruiters->take(4); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $company): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                                    <div class="w-12 h-12 flex-shrink-0">
                                        <?php if($company->logo): ?>
                                            <img src="<?php echo e(url('storage/' . $company->logo)); ?>" alt="<?php echo e($company->name); ?>" class="w-12 h-12 object-contain rounded-lg border border-gray-200 bg-white">
                                        <?php else: ?>
                                            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                                                <i class="fa fa-building text-white"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-semibold text-gray-900 truncate"><?php echo e($company->name); ?></p>
                                        <p class="text-xs text-blue-600 font-medium"><?php echo e($company->job_listings_count); ?> Jobs Available</p>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                            <div class="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                                <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center">
                                    <i class="fa fa-building text-white"></i>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-semibold text-gray-900 truncate">Microsoft</p>
                                    <p class="text-xs text-blue-600 font-medium">45 Jobs Available</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                                    <i class="fa fa-building text-white"></i>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-semibold text-gray-900 truncate">Google</p>
                                    <p class="text-xs text-blue-600 font-medium">38 Jobs Available</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                                <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center">
                                    <i class="fa fa-building text-white"></i>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-semibold text-gray-900 truncate">Amazon</p>
                                    <p class="text-xs text-blue-600 font-medium">52 Jobs Available</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                                <div class="w-12 h-12 bg-gradient-to-br from-gray-500 to-gray-600 rounded-lg flex items-center justify-center">
                                    <i class="fa fa-building text-white"></i>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-semibold text-gray-900 truncate">Apple</p>
                                    <p class="text-xs text-blue-600 font-medium">29 Jobs Available</p>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Training Institutes -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-gray-900">Training Institutes</h3>
                        <div class="w-8 h-8 red-gradient rounded-lg flex items-center justify-center">
                            <i class="fa fa-graduation-cap text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <div class="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                            <div class="w-12 h-12 bg-gradient-to-br from-teal-500 to-teal-600 rounded-lg flex items-center justify-center">
                                <i class="fa fa-graduation-cap text-white"></i>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-semibold text-gray-900 truncate">Tech Academy</p>
                                <p class="text-xs text-red-600 font-medium">15 Courses</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                            <div class="w-12 h-12 bg-gradient-to-br from-pink-500 to-pink-600 rounded-lg flex items-center justify-center">
                                <i class="fa fa-graduation-cap text-white"></i>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-semibold text-gray-900 truncate">Skill Development Center</p>
                                <p class="text-xs text-red-600 font-medium">22 Courses</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                            <div class="w-12 h-12 bg-gradient-to-br from-cyan-500 to-cyan-600 rounded-lg flex items-center justify-center">
                                <i class="fa fa-graduation-cap text-white"></i>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-semibold text-gray-900 truncate">Professional Institute</p>
                                <p class="text-xs text-red-600 font-medium">18 Courses</p>
                            </div>
                        </div>
                        <div class="pt-4 border-t border-gray-100">
                            <span class="block text-center text-gray-400 font-medium text-sm">
                                Coming Soon
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('frontend.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\joboctopus\resources\views/frontend/index.blade.php ENDPATH**/ ?>